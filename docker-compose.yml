services:
  drools-workbench:
    image: jboss/drools-workbench:7.17.0.Final
    container_name: drools-workbench
    ports:
      - "0.0.0.0:8080:8080"
    environment:
      - KIE_SERVER_USER=admin
      - KIE_SERVER_PWD=admin
      - WORKBENCH_USER=admin
      - WORKBENCH_PWD=admin
    networks:
      - drools-net
  kie-server:
    image: jboss/kie-server:7.17.0.Final
    container_name: kie-server
    ports:
      - "0.0.0.0:8180:8080"
    environment:
      - KIE_SERVER_ID=kie-server
      - KIE_SERVER_LOCATION=http://kie-server:8080/kie-server/services/rest/server
      - KIE_SERVER_CONTROLLER=http://drools-workbench:8080/business-central/rest/controller
      - KIE_MAVEN_REPO=http://drools-workbench:8080/maven2
      - KIE_SERVER_USER=admin123
      - KIE_SERVER_PWD=admin123!
    depends_on:
      - drools-workbench
    networks:
      - drools-net
networks:
  drools-net:
    driver: bridge